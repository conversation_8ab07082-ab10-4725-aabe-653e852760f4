import React, { useState, useEffect } from 'react';
import {
  Avatar,
  Form,
  Input,
  Upload,
  message,
  Tabs,
  List,
  Tag,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  Badge,
  Modal,
  Divider
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  CameraOutlined,
  ShoppingOutlined,
  HeartOutlined,
  MessageOutlined,
  SettingOutlined,
  TrophyOutlined,
  StarFilled,
  EyeOutlined,
  BookOutlined,
  SafetyOutlined,
  ThunderboltOutlined,
  CrownOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useAuthStore } from '../../stores/authStore';
import { useNavigate } from 'react-router-dom';
import { usersService } from '../../services/users';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { theme } from '../../styles/theme';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const ProfileContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${theme.spacing[6]};

  .profile-header {
    background: ${theme.colors.gradients.primary};
    border-radius: ${theme.borderRadius['3xl']};
    padding: ${theme.spacing[12]} ${theme.spacing[10]};
    margin-bottom: ${theme.spacing[8]};
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: ${theme.boxShadow['2xl']};
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('/images/pattern.png') repeat;
      opacity: 0.1;
    }
    
    .header-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      gap: 24px;
      
      @media (max-width: 768px) {
        flex-direction: column;
        text-align: center;
      }
      
      .avatar-section {
        position: relative;
        
        .user-avatar {
          width: 120px;
          height: 120px;
          border: 4px solid rgba(255, 255, 255, 0.3);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }
        
        .avatar-upload {
          position: absolute;
          bottom: 0;
          right: 0;
          background: #1677ff;
          border-radius: 50%;
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          
          &:hover {
            background: #0958d9;
          }
        }
      }
      
      .user-info {
        flex: 1;
        
        .user-name {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 8px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .user-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          margin-bottom: 16px;
          
          .meta-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            opacity: 0.9;
          }
        }
        
        .user-level {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          background: rgba(255, 255, 255, 0.2);
          padding: 6px 12px;
          border-radius: 20px;
          backdrop-filter: blur(10px);
          
          .level-icon {
            color: #ffd700;
          }
        }
      }
      
      .stats-section {
        display: flex;
        gap: 32px;
        
        @media (max-width: 768px) {
          justify-content: center;
        }
        
        .stat-item {
          text-align: center;
          
          .stat-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 12px;
            opacity: 0.8;
          }
        }
      }
    }
  }
  
  .profile-content {
    .profile-tabs {
      .ant-tabs-nav {
        margin-bottom: 24px;
        
        .ant-tabs-tab {
          padding: 12px 20px;
          font-weight: 500;
          
          .tab-icon {
            margin-right: 8px;
          }
        }
      }
      
      .tab-content {
        min-height: 400px;
      }
    }
  }
  
  .info-card {
    .card-header {
      display: flex;
      justify-content: between;
      align-items: center;
      margin-bottom: 16px;
      
      .header-title {
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .form-section {
      .ant-form-item {
        margin-bottom: 20px;
      }
    }
  }
  
  .order-item {
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #1677ff;
      box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
    }
    
    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      .order-number {
        font-weight: 600;
        color: #262626;
      }
      
      .order-status {
        font-size: 12px;
      }
    }
    
    .order-books {
      display: flex;
      gap: 12px;
      margin-bottom: 12px;
      
      .book-cover {
        width: 60px;
        height: 75px;
        border-radius: 4px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    
    .order-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .order-total {
        font-size: 16px;
        font-weight: 600;
        color: #ff4d4f;
      }
      
      .order-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
  
  .favorite-item {
    .book-info {
      display: flex;
      gap: 12px;
      
      .book-cover {
        width: 80px;
        height: 100px;
        border-radius: 6px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .book-details {
        flex: 1;
        
        .book-title {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 8px;
          cursor: pointer;
          
          &:hover {
            color: #1677ff;
          }
        }
        
        .book-meta {
          color: #8c8c8c;
          font-size: 14px;
          margin-bottom: 8px;
        }
        
        .book-price {
          font-size: 18px;
          font-weight: 700;
          color: #ff4d4f;
        }
      }
    }
  }
  
  .achievement-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    
    .achievement-card {
      text-align: center;
      padding: 20px;
      border: 1px solid #f0f0f0;
      border-radius: 12px;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #1677ff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(22, 119, 255, 0.1);
      }
      
      &.achieved {
        background: linear-gradient(135deg, #f6ffed, #e6f4ff);
        border-color: #52c41a;
      }
      
      .achievement-icon {
        font-size: 32px;
        margin-bottom: 12px;
        
        &.achieved {
          color: #52c41a;
        }
        
        &.locked {
          color: #d9d9d9;
        }
      }
      
      .achievement-title {
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .achievement-desc {
        font-size: 12px;
        color: #8c8c8c;
      }
      
      .achievement-progress {
        margin-top: 12px;
      }
    }
  }
`;

interface UserProfileProps {
  className?: string;
}

const UserProfile: React.FC<UserProfileProps> = ({ className }) => {
  const { user, updateUser } = useAuthStore();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('info');

  // 模拟数据
  const [orders] = useState([
    {
      id: '1',
      order_number: 'ORD20240101001',
      status: 'delivered',
      total_amount: 89.90,
      created_at: '2024-01-01',
      books: [
        { id: '1', title: '深入理解计算机系统', cover_image: '/images/book1.jpg' },
        { id: '2', title: 'JavaScript高级程序设计', cover_image: '/images/book2.jpg' }
      ]
    }
  ]);

  const [favorites] = useState([
    {
      id: '1',
      title: 'React技术揭秘',
      author: '卡颂',
      price: 79.00,
      cover_image: '/images/book3.jpg',
      created_at: '2024-01-01'
    }
  ]);

  const achievements = [
    {
      id: 'first_order',
      title: '初次购买',
      description: '完成第一笔订单',
      icon: <ShoppingOutlined />,
      achieved: true,
      progress: 100
    },
    {
      id: 'book_lover',
      title: '图书爱好者',
      description: '购买10本图书',
      icon: <BookOutlined />,
      achieved: false,
      progress: 60,
      current: 6,
      target: 10
    },
    {
      id: 'reviewer',
      title: '评论达人',
      description: '发表5条评论',
      icon: <MessageOutlined />,
      achieved: true,
      progress: 100
    },
    {
      id: 'collector',
      title: '收藏家',
      description: '收藏20本图书',
      icon: <HeartOutlined />,
      achieved: false,
      progress: 25,
      current: 5,
      target: 20
    }
  ];

  useEffect(() => {
    if (user) {
      form.setFieldsValue({
        username: user.username,
        email: user.email,
        phone: user.phone
      });
    }
  }, [user, form]);

  const handleSaveProfile = async (values: any) => {
    try {
      setLoading(true);
      const response = await usersService.updateProfile(values);
      if (response.success && response.data) {
        updateUser(response.data);
        setEditMode(false);
        message.success('个人信息更新成功');
      } else {
        message.error(response.message || '更新失败');
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || '更新失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success('头像上传成功');
      // 更新用户头像
    } else if (info.file.status === 'error') {
      message.error('头像上传失败');
    }
  };

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: 'orange',
      paid: 'blue',
      delivering: 'cyan',
      delivered: 'green',
      cancelled: 'red'
    };
    return colorMap[status] || 'default';
  };

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      pending: '待支付',
      paid: '已支付',
      delivering: '配送中',
      delivered: '已送达',
      cancelled: '已取消'
    };
    return textMap[status] || status;
  };

  if (!user) {
    return null;
  }

  return (
    <ProfileContainer className={className}>
      {/* 个人信息头部 */}
      <div className="profile-header">
        <div className="header-content">
          <div className="avatar-section">
            <Avatar
              className="user-avatar"
              src={user.avatar}
              icon={<UserOutlined />}
            />
            <Upload
              showUploadList={false}
              action="/api/upload/avatar"
              onChange={handleAvatarUpload}
            >
              <div className="avatar-upload">
                <CameraOutlined style={{ color: 'white' }} />
              </div>
            </Upload>
          </div>
          
          <div className="user-info">
            <div className="user-name">{user.username}</div>
            <div className="user-meta">
              <div className="meta-item">
                <UserOutlined />
                会员编号: {user.id.slice(-8)}
              </div>
              <div className="meta-item">
                <MessageOutlined />
                {user.email}
              </div>
            </div>
            <div className="user-level">
              <TrophyOutlined className="level-icon" />
              <span>黄金会员</span>
            </div>
          </div>
          
          <div className="stats-section">
            <div className="stat-item">
              <div className="stat-value">12</div>
              <div className="stat-label">订单数</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">8</div>
              <div className="stat-label">收藏数</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">5</div>
              <div className="stat-label">评论数</div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="profile-content">
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          className="profile-tabs"
        >
          <TabPane 
            tab={<span><UserOutlined className="tab-icon" />个人信息</span>} 
            key="info"
          >
            <Card className="info-card">
              <div className="card-header">
                <Title level={4} className="header-title">基本信息</Title>
                <Button
                  type={editMode ? 'default' : 'primary'}
                  icon={<EditOutlined />}
                  onClick={() => setEditMode(!editMode)}
                >
                  {editMode ? '取消编辑' : '编辑信息'}
                </Button>
              </div>
              
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSaveProfile}
                className="form-section"
              >
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      name="username"
                      label="用户名"
                      rules={[{ required: true, message: '请输入用户名' }]}
                    >
                      <Input disabled={!editMode} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="email"
                      label="邮箱"
                      rules={[
                        { required: true, message: '请输入邮箱' },
                        { type: 'email', message: '请输入有效的邮箱地址' }
                      ]}
                    >
                      <Input disabled={!editMode} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="phone"
                      label="手机号"
                      rules={[{ required: true, message: '请输入手机号' }]}
                    >
                      <Input disabled={!editMode} />
                    </Form.Item>
                  </Col>

                </Row>
                
                {editMode && (
                  <Form.Item>
                    <Button
                      variant="gradient"
                      gradient="primary"
                      htmlType="submit"
                      loading={loading}
                      rounded
                      elevated
                    >
                      保存修改
                    </Button>
                  </Form.Item>
                )}
              </Form>
            </Card>
          </TabPane>

          <TabPane 
            tab={<span><ShoppingOutlined className="tab-icon" />我的订单</span>} 
            key="orders"
          >
            <Card>
              <List
                dataSource={orders}
                renderItem={(order) => (
                  <div className="order-item">
                    <div className="order-header">
                      <span className="order-number">订单号: {order.order_number}</span>
                      <Tag color={getStatusColor(order.status)}>
                        {getStatusText(order.status)}
                      </Tag>
                    </div>
                    
                    <div className="order-books">
                      {order.books.map(book => (
                        <div key={book.id} className="book-cover">
                          <img src={book.cover_image} alt={book.title} />
                        </div>
                      ))}
                    </div>
                    
                    <div className="order-footer">
                      <span className="order-total">¥{order.total_amount.toFixed(2)}</span>
                      <div className="order-actions">
                        <Button
                          variant="ghost"
                          size="small"
                          onClick={() => navigate(`/orders/${order.id}`)}
                          rounded
                        >
                          查看详情
                        </Button>
                        {order.status === 'delivered' && (
                          <Button
                            variant="gradient"
                            gradient="primary"
                            size="small"
                            rounded
                          >
                            评价
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              />
            </Card>
          </TabPane>

          <TabPane 
            tab={<span><HeartOutlined className="tab-icon" />我的收藏</span>} 
            key="favorites"
          >
            <Card>
              <List
                dataSource={favorites}
                renderItem={(book) => (
                  <List.Item className="favorite-item">
                    <div className="book-info">
                      <div className="book-cover">
                        <img src={book.cover_image} alt={book.title} />
                      </div>
                      <div className="book-details">
                        <div 
                          className="book-title"
                          onClick={() => navigate(`/books/${book.id}`)}
                        >
                          {book.title}
                        </div>
                        <div className="book-meta">
                          作者: {book.author} · 收藏于 {book.created_at}
                        </div>
                        <div className="book-price">¥{book.price.toFixed(2)}</div>
                      </div>
                    </div>
                    <Button
                      variant="gradient"
                      gradient="primary"
                      size="small"
                      rounded
                    >
                      加入购物车
                    </Button>
                  </List.Item>
                )}
              />
            </Card>
          </TabPane>

          <TabPane 
            tab={<span><TrophyOutlined className="tab-icon" />成就徽章</span>} 
            key="achievements"
          >
            <Card>
              <div className="achievement-grid">
                {achievements.map(achievement => (
                  <div 
                    key={achievement.id} 
                    className={`achievement-card ${achievement.achieved ? 'achieved' : ''}`}
                  >
                    <div className={`achievement-icon ${achievement.achieved ? 'achieved' : 'locked'}`}>
                      {achievement.icon}
                    </div>
                    <div className="achievement-title">{achievement.title}</div>
                    <div className="achievement-desc">{achievement.description}</div>
                    {!achievement.achieved && achievement.current !== undefined && (
                      <div className="achievement-progress">
                        <Progress 
                          percent={achievement.progress} 
                          size="small"
                          format={() => `${achievement.current}/${achievement.target}`}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </Card>
          </TabPane>

          <TabPane 
            tab={<span><SettingOutlined className="tab-icon" />账户设置</span>} 
            key="settings"
          >
            <Card>
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div>
                  <Title level={5}>安全设置</Title>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>登录密码</span>
                      <Button type="link">修改密码</Button>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>手机绑定</span>
                      <Button type="link">更换手机</Button>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>邮箱绑定</span>
                      <Button type="link">更换邮箱</Button>
                    </div>
                  </Space>
                </div>
                
                <Divider />
                
                <div>
                  <Title level={5}>隐私设置</Title>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>个人资料公开</span>
                      <Button type="link">设置</Button>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>购买记录公开</span>
                      <Button type="link">设置</Button>
                    </div>
                  </Space>
                </div>
                
                <Divider />
                
                <div>
                  <Title level={5}>通知设置</Title>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>订单通知</span>
                      <Button type="link">设置</Button>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>促销通知</span>
                      <Button type="link">设置</Button>
                    </div>
                  </Space>
                </div>
              </Space>
            </Card>
          </TabPane>
        </Tabs>
      </div>
    </ProfileContainer>
  );
};

export default UserProfile;
